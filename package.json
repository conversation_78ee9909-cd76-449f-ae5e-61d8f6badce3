{"name": "quasari-web-search", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "convex dev --once && node setup.mjs && npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "build": "vite build", "preview": "vite preview", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"}, "dependencies": {"@auth/core": "^0.37.4", "@codemirror/lang-javascript": "^6.2.4", "@convex-dev/auth": "^0.0.87", "@google/genai": "^1.0.1", "@gsap/react": "^2.1.1", "@modelcontextprotocol/sdk": "^1.12.0", "@uiw/codemirror-theme-okaidia": "^4.24.1", "@uiw/react-codemirror": "^4.24.1", "clsx": "^2.1.1", "convex": "1.21.1-alpha.1", "gsap": "^3.12.5", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.30.1", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "monaco-editor": "^0.52.2", "netlify-cli": "^21.6.0", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "~3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}