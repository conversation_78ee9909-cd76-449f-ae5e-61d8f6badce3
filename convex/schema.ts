import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  searches: defineTable({
    userId: v.id("users"),
    sessionId: v.optional(v.string()), // Link to conversation session
    query: v.string(),
    results: v.string(), // JSON string of search results
    aiResponse: v.string(),
    timestamp: v.number(),
  }).index("by_user", ["userId"])
    .index("by_session", ["sessionId"]),

  sessions: defineTable({
    userId: v.id("users"),
    sessionId: v.string(), // Unique session identifier
    title: v.optional(v.string()), // Auto-generated title from first query
    conversationHistory: v.string(), // JSON string of full conversation history
    lastActivity: v.number(),
    createdAt: v.number(),
    messageCount: v.number(), // Number of messages in this session
  }).index("by_user", ["userId"])
    .index("by_sessionId", ["sessionId"])
    .index("by_user_activity", ["userId", "lastActivity"]),

  searchProgress: defineTable({
    searchId: v.string(),
    step: v.string(), // "searching", "analyzing", "scraping", "processing", "finalizing", "complete", "error"
    progress: v.number(), // 0-100
    message: v.string(),
    sourceUrl: v.optional(v.string()), // URL being scraped
    sourceTitle: v.optional(v.string()), // Title of the source being scraped
    finalResult: v.optional(v.any()), // Final search result when complete
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_searchId", ["searchId"]),

  gmailCredentials: defineTable({
    userId: v.id("users"),
    credentials: v.string(), // JSON string of OAuth2 credentials
    refreshToken: v.string(),
    accessToken: v.string(),
    expiryDate: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  monacoSessions: defineTable({
    userId: v.id("users"),
    sessionId: v.string(), // Monaco chatbot session identifier
    prompt: v.string(), // User's original prompt
    generatedCode: v.string(), // AI-generated code
    timestamp: v.number(), // When the code was generated
    createdAt: v.number(),
  }).index("by_user", ["userId"])
    .index("by_session", ["sessionId"])
    .index("by_user_timestamp", ["userId", "timestamp"]),

  files: defineTable({
    name: v.string(),
    content: v.string(),
  }),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
