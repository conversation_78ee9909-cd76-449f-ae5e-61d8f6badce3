// import { useState, useEffect } from "react";
// import { useAction, useQuery } from "convex/react";
// import { api } from "../convex/_generated/api";
// import { toast } from "sonner";
// import "./formatted-response.css";

// // Function to format response for HTML display
// function formatResponseForDisplay(response: string): string {
//   let formatted = response;

//   // First, handle JSON code blocks
//   formatted = formatted.replace(/```json\n?([\s\S]*?)\n?```/g, (match, jsonContent) => {
//     try {
//       // Try to parse and format the JSON
//       const parsed = JSON.parse(jsonContent.trim());
//       const prettyJson = JSON.stringify(parsed, null, 2);

//       // Add syntax highlighting to JSON
//       const highlightedJson = prettyJson
//         .replace(/(".*?")\s*:/g, '<span class="json-key">$1</span>:')
//         .replace(/:\s*(".*?")/g, ': <span class="json-string">$1</span>')
//         .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
//         .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>')
//         .replace(/:\s*(\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
//         .replace(/([{}[\]])/g, '<span class="json-bracket">$1</span>');

//       const copyId = `json-${Math.random().toString(36).substring(2, 11)}`;
//       return `<div class="json-block">
//         <div class="json-header">
//           <span>📄 JSON Response</span>
//         </div>
//         <button class="copy-button" onclick="navigator.clipboard.writeText(document.getElementById('${copyId}').textContent); this.textContent='✅ Copied!'; setTimeout(() => this.textContent='📋 Copy', 2000)">📋 Copy</button>
//         <pre class="json-content"><code id="${copyId}" style="display:none;">${prettyJson}</code><div class="json-highlighted">${highlightedJson}</div></pre>
//       </div>`;
//     } catch (e) {
//       // If parsing fails, just show as code block
//       const copyId = `code-${Math.random().toString(36).substring(2, 11)}`;
//       return `<div class="code-block">
//         <div class="code-header">
//           <span>📄 JSON</span>
//         </div>
//         <button class="copy-button" onclick="navigator.clipboard.writeText(document.getElementById('${copyId}').textContent); this.textContent='✅ Copied!'; setTimeout(() => this.textContent='📋 Copy', 2000)">📋 Copy</button>
//         <pre class="code-content"><code id="${copyId}">${jsonContent.trim()}</code></pre>
//       </div>`;
//     }
//   });

//   // Handle other code blocks
//   formatted = formatted.replace(/```(\w+)?\n?([\s\S]*?)\n?```/g, (match, language, codeContent) => {
//     const lang = language || 'text';
//     const copyId = `code-${Math.random().toString(36).substring(2, 11)}`;
//     return `<div class="code-block">
//       <div class="code-header">
//         ${lang.toUpperCase()}
//       </div>
//       <button class="copy-button" onclick="navigator.clipboard.writeText(document.getElementById('${copyId}').textContent); this.textContent='Copied!'; setTimeout(() => this.textContent='Copy', 2000)">Copy</button>
//       <pre class="code-content"><code id="${copyId}">${codeContent.trim()}</code></pre>
//     </div>`;
//   });

//   // Convert markdown-style formatting to HTML
//   formatted = formatted
//     // Convert headers
//     .replace(/^## (.+)$/gm, '<h2 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1</h2>')
//     .replace(/^### (.+)$/gm, '<h3 class="text-base font-medium text-gray-800 mt-3 mb-2">$1</h3>')
//     // Convert bullet points
//     .replace(/^• (.+)$/gm, '<li class="ml-4 mb-1">$1</li>')
//     // Convert numbered lists
//     .replace(/^(\d+)\. (.+)$/gm, '<li class="ml-4 mb-1 list-decimal">$2</li>')
//     // Convert links
//     .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>')
//     // Convert bold text
//     .replace(/\*\*([^*]+)\*\*/g, '<strong class="font-semibold">$1</strong>')
//     // Convert italic text
//     .replace(/\*([^*]+)\*/g, '<em class="italic">$1</em>')
//     // Convert line breaks to proper spacing
//     .replace(/\n\n/g, '</p><p class="mb-3">')
//     // Handle remaining single line breaks
//     .replace(/\n/g, '<br/>');

//   // Wrap in paragraphs and handle lists
//   formatted = formatted
//     // Wrap bullet points in ul tags
//     .replace(/(<li class="ml-4 mb-1">.*?<\/li>)(?:\s*<br\/>)*(?=<li class="ml-4 mb-1">|$)/gs, '<ul class="list-disc ml-6 mb-3 space-y-1">$1</ul>')
//     // Wrap numbered lists in ol tags
//     .replace(/(<li class="ml-4 mb-1 list-decimal">.*?<\/li>)(?:\s*<br\/>)*(?=<li class="ml-4 mb-1 list-decimal">|$)/gs, '<ol class="list-decimal ml-6 mb-3 space-y-1">$1</ol>')
//     // Clean up multiple consecutive list items
//     .replace(/<\/ul>\s*<ul class="list-disc ml-6 mb-3 space-y-1">/g, '')
//     .replace(/<\/ol>\s*<ol class="list-decimal ml-6 mb-3 space-y-1">/g, '')
//     // Wrap remaining content in paragraphs
//     .replace(/^(?!<[hulo])/gm, '<p class="mb-3 leading-relaxed">')
//     .replace(/(?<!>)$/gm, '</p>')
//     // Clean up empty paragraphs
//     .replace(/<p class="mb-3 leading-relaxed"><\/p>/g, '')
//     // Clean up extra breaks
//     .replace(/<br\/>\s*<\/p>/g, '</p>')
//     .replace(/<p class="mb-3 leading-relaxed">\s*<br\/>/g, '<p class="mb-3 leading-relaxed">');

//   return formatted;
// }

// interface SearchProgress {
//   searchId: string;
//   step: string;
//   progress: number;
//   message: string;
//   sourceUrl?: string;
//   sourceTitle?: string;
//   createdAt: number;
//   updatedAt: number;
// }

// export function SearchAgent() {
//   const [query, setQuery] = useState("");
//   const [isSearching, setIsSearching] = useState(false);
//   const [currentSearchId, setCurrentSearchId] = useState<string | null>(null);
//   const [completedSearchIds, setCompletedSearchIds] = useState<Set<string>>(new Set());
//   const [showCompletionAnimation, setShowCompletionAnimation] = useState(false);
//   const [currentResult, setCurrentResult] = useState<{
//     query: string;
//     response: string;
//     timestamp: number;
//     toolCalls?: number;
//     sourcesScraped?: number;
//     complexity?: 'simple' | 'moderate' | 'complex';
//     reasoning?: string;
//   } | null>(null);

//   const searchWebWithProgress = useAction(api.search.searchWebWithProgress);
//   const searches = useQuery(api.queries.getUserSearches) || [];
//   const searchProgress = useQuery(
//     api.queries.getSearchProgress,
//     currentSearchId ? { searchId: currentSearchId } : "skip"
//   );

//   // Reset search state on component mount - comprehensive cleanup
//   useEffect(() => {
//     // Always reset on mount to prevent stale state
//     setIsSearching(false);
//     setCurrentSearchId(null);
//     setCompletedSearchIds(new Set());
//     setShowCompletionAnimation(false);
//   }, []); // Only run on mount

//   // Handle search completion and show results with duplicate prevention
//   useEffect(() => {
//     if (searchProgress?.step === "complete" &&
//         isSearching &&
//         currentSearchId &&
//         searchProgress.searchId === currentSearchId &&
//         !completedSearchIds.has(currentSearchId)) {

//       // Mark this search as completed to prevent duplicates
//       setCompletedSearchIds(prev => new Set(prev).add(currentSearchId));

//       // Check if we have a final result in the progress
//       const finalResult = (searchProgress as any)?.finalResult;
//       if (finalResult) {
//         setCurrentResult({
//           query: finalResult.query,
//           response: finalResult.response,
//           timestamp: finalResult.timestamp,
//           toolCalls: finalResult.toolCalls,
//           sourcesScraped: finalResult.sourcesScraped
//         });
//         toast.success(`Search completed! Used ${finalResult.toolCalls || 1} tool calls and scraped ${finalResult.sourcesScraped || 0} sources.`);
//       }

//       // Show completion animation briefly, then reset
//       setShowCompletionAnimation(true);

//       // Immediate state reset for responsive UI
//       setIsSearching(false);
//       setCurrentSearchId(null);

//       // Hide completion animation after a short delay
//       const animationTimer = setTimeout(() => {
//         setShowCompletionAnimation(false);
//       }, 1500);
//       return () => clearTimeout(animationTimer);
//     }
//   }, [searchProgress?.step, searchProgress?.searchId, searchProgress, isSearching, currentSearchId, completedSearchIds]);

//   const handleSearch = async (e: React.FormEvent) => {
//     e.preventDefault();
//     if (!query.trim()) return;

//     // Validate query length
//     const trimmedQuery = query.trim();
//     if (trimmedQuery.length > 3000) {
//       toast.error("Query too long! Please keep your question under 3000 characters.");
//       return;
//     }

//     if (trimmedQuery.length < 3) {
//       toast.error("Query too short! Please enter at least 3 characters.");
//       return;
//     }

//     const searchId = `search_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
//     setCurrentSearchId(searchId);
//     setIsSearching(true);
//     setCurrentResult(null);
//     setShowCompletionAnimation(false);
//     // Clear any previous completion tracking for this new search
//     setCompletedSearchIds(prev => {
//       const newSet = new Set(prev);
//       newSet.delete(searchId); // Ensure this new ID is not marked as completed
//       return newSet;
//     });

//     try {
//       const result = await searchWebWithProgress({
//         query: trimmedQuery,
//         searchId
//       });

//       // Check if search started successfully
//       if (result.status === "started") {
//         setQuery("");
//         toast.success("Search started! Check progress below for updates.");
//         // The search will continue in background, progress will be tracked via searchProgress
//       } else {
//         // Handle legacy response format (shouldn't happen with new implementation)
//         setCurrentResult(result as any);
//         setQuery("");
//         setIsSearching(false);
//         setCurrentSearchId(null);
//         toast.success(`Search completed! Used ${(result as any).toolCalls || 1} tool calls and scraped ${(result as any).sourcesScraped || 0} sources.`);
//       }
//     } catch (error) {
//       const errorMessage = error instanceof Error ? error.message : "Search failed. Please try again.";
//       toast.error(errorMessage);
//       console.error("Search error:", error);

//       // Clean up search state immediately on error
//       setIsSearching(false);
//       setCurrentSearchId(null);
//     }
//   };

//   return (
//     <div className="space-y-12">
//       {/* Search Form */}
//       <div className="max-w-4xl mx-auto">
//         <form onSubmit={handleSearch} className="space-y-6">
//           <div className="space-y-4">
//             <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-stretch">
//               <div className="flex-1 relative">
//                 <textarea
//                   value={query}
//                   onChange={(e) => setQuery(e.target.value)}
//                   placeholder="Ask me anything... (e.g., 'What's the latest news about AI?')"
//                   className={`w-full px-6 py-4 bg-white border-2 resize-none focus:ring-4 outline-none transition-all duration-200 text-black placeholder-gray-500 text-lg font-normal ${
//                     query.length > 3000
//                       ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
//                       : query.length > 2500
//                       ? 'border-yellow-500 focus:border-yellow-500 focus:ring-yellow-500/20'
//                       : 'border-gray-300 focus:border-black focus:ring-black/10'
//                   }`}
//                   style={{
//                     borderRadius: '32px',
//                     letterSpacing: '-0.01em'
//                   }}
//                   disabled={isSearching}
//                   rows={query.length > 100 ? (query.length > 500 ? 4 : 3) : 2}
//                   maxLength={3100}
//                 />
//                 {query.length > 0 && (
//                   <div className={`absolute bottom-3 right-3 text-xs px-3 py-1 font-medium ${
//                     query.length > 3000
//                       ? 'bg-red-100 text-red-700'
//                       : query.length > 2500
//                       ? 'bg-yellow-100 text-yellow-800'
//                       : 'bg-gray-100 text-gray-600'
//                   }`}
//                   style={{ borderRadius: '16px' }}>
//                     {query.length}/3000
//                   </div>
//                 )}
//               </div>
//               <button
//                 type="submit"
//                 disabled={isSearching || !query.trim() || query.length > 3000 || query.length < 3}
//                 className="px-8 py-4 text-black font-medium text-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-150 md:self-start whitespace-nowrap"
//                 style={{
//                   backgroundColor: '#E8FF59',
//                   borderRadius: '32px',
//                   border: 'none',
//                   letterSpacing: '-0.01em',
//                   minHeight: '56px'
//                 }}
//               >
//                 {isSearching ? (
//                   <div className="flex items-center gap-3">
//                     <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
//                     Searching...
//                   </div>
//                 ) : (
//                   "Search"
//                 )}
//               </button>
//             </div>
//             {query.length > 2500 && (
//               <div className={`text-base font-bold ${
//                 query.length > 3000 ? 'text-red-600' : 'text-yellow-700'
//               }`}>
//                 {query.length > 3000
//                   ? '⚠️ Query too long! Please shorten your question.'
//                   : '⚠️ Query is getting long. Consider shortening for better results.'}
//               </div>
//             )}
//           </div>
//         </form>
//       </div>

//       {/* Progress Indicator */}
//       {(isSearching || showCompletionAnimation) && searchProgress && (
//         <div className={`quasari-card p-10 border-2 transition-all duration-500 ${
//           searchProgress.step === "complete" ? 'border-green-500 bg-green-50' : 'border-yellow'
//         }`}>
//           <div className="flex items-center justify-between mb-8">
//             <h3 className={`text-2xl font-black quasari-text ${
//               searchProgress.step === "complete" ? 'text-green-800' : ''
//             }`}>
//               {searchProgress.step === "complete" ? 'Search Complete!' : 'Search in Progress'}
//             </h3>
//             <span className={`text-xl font-black text-dark px-6 py-3 ${
//               searchProgress.step === "complete" ? 'bg-green-500 text-white' : 'bg-yellow'
//             }`} style={{ borderRadius: '20px' }}>
//               {searchProgress.progress}%
//             </span>
//           </div>

//           {/* Progress Bar */}
//           <div className="w-full bg-gray-200 h-6 mb-8" style={{ borderRadius: '12px' }}>
//             <div
//               className="quasari-yellow h-6 transition-all duration-500 ease-out"
//               style={{ width: `${searchProgress.progress}%`, borderRadius: '12px' }}
//             ></div>
//           </div>

//           {/* Current Step */}
//           <div className="flex items-center gap-6">
//             <div className="flex-shrink-0">
//               {searchProgress.step === "searching" && (
//                 <div className="animate-spin rounded-full h-8 w-8 border-b-3 border-dark"></div>
//               )}
//               {searchProgress.step === "analyzing" && (
//                 <div className="animate-pulse">
//                   <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
//                   </svg>
//                 </div>
//               )}
//               {searchProgress.step === "scraping" && (
//                 <div className="animate-bounce">
//                   <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
//                   </svg>
//                 </div>
//               )}
//               {searchProgress.step === "processing" && (
//                 <div className="animate-pulse">
//                   <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
//                   </svg>
//                 </div>
//               )}
//               {searchProgress.step === "finalizing" && (
//                 <div className="animate-spin">
//                   <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
//                   </svg>
//                 </div>
//               )}
//               {searchProgress.step === "complete" && (
//                 <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
//                 </svg>
//               )}
//               {searchProgress.step === "error" && (
//                 <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                 </svg>
//               )}
//             </div>
//             <div className="flex-1">
//               <p className="quasari-text font-black text-2xl capitalize">
//                 {searchProgress.step.replace(/([A-Z])/g, ' $1').trim()}
//               </p>
//               <p className="quasari-text-muted text-lg font-medium">
//                 {searchProgress.message}
//               </p>
//               {/* Show source information when scraping */}
//               {searchProgress.step === "scraping" && searchProgress.sourceUrl && (
//                 <div className="mt-6 p-6 bg-white border-2 border-gray-200 shadow-sm" style={{ borderRadius: '20px' }}>
//                   <div className="flex items-start gap-4">
//                     <div className="flex-shrink-0 mt-1">
//                       <svg className="h-6 w-6 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
//                       </svg>
//                     </div>
//                     <div className="flex-1 min-w-0">
//                       <p className="quasari-text font-bold text-base leading-tight">
//                         {searchProgress.sourceTitle || "Unknown Title"}
//                       </p>
//                       <p className="quasari-text-muted text-sm mt-2 truncate" title={searchProgress.sourceUrl}>
//                         {(() => {
//                           try {
//                             return new URL(searchProgress.sourceUrl).hostname;
//                           } catch {
//                             return searchProgress.sourceUrl;
//                           }
//                         })()}
//                       </p>
//                     </div>
//                     <div className="flex-shrink-0">
//                       <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-dark"></div>
//                     </div>
//                   </div>
//                 </div>
//               )}
//             </div>
//           </div>

//           {/* Enhanced Step Indicators */}
//           <div className="mt-10 grid grid-cols-6 gap-4 text-base">
//             <div className={`flex flex-col items-center ${searchProgress.progress >= 10 ? 'text-dark font-black' : 'text-gray-500 font-medium'}`}>
//               <div className={`w-4 h-4 rounded-full mb-3 ${searchProgress.progress >= 10 ? 'bg-yellow' : 'bg-gray-300'}`}></div>
//               <span className="text-center">Search</span>
//             </div>
//             <div className={`flex flex-col items-center ${searchProgress.progress >= 30 ? 'text-dark font-black' : 'text-gray-500 font-medium'}`}>
//               <div className={`w-4 h-4 rounded-full mb-3 ${searchProgress.progress >= 30 ? 'bg-yellow' : 'bg-gray-300'}`}></div>
//               <span className="text-center">Analyze</span>
//             </div>
//             <div className={`flex flex-col items-center ${searchProgress.progress >= 40 ? 'text-dark font-black' : 'text-gray-500 font-medium'}`}>
//               <div className={`w-4 h-4 rounded-full mb-3 ${searchProgress.progress >= 40 ? 'bg-yellow' : 'bg-gray-300'}`}></div>
//               <span className="text-center">Scrape</span>
//             </div>
//             <div className={`flex flex-col items-center ${searchProgress.progress >= 80 ? 'text-dark font-black' : 'text-gray-500 font-medium'}`}>
//               <div className={`w-4 h-4 rounded-full mb-3 ${searchProgress.progress >= 80 ? 'bg-yellow' : 'bg-gray-300'}`}></div>
//               <span className="text-center">Research</span>
//             </div>
//             <div className={`flex flex-col items-center ${searchProgress.progress >= 90 ? 'text-dark font-black' : 'text-gray-500 font-medium'}`}>
//               <div className={`w-4 h-4 rounded-full mb-3 ${searchProgress.progress >= 90 ? 'bg-yellow' : 'bg-gray-300'}`}></div>
//               <span className="text-center">Process</span>
//             </div>
//             <div className={`flex flex-col items-center ${searchProgress.progress >= 100 ? 'text-green-800 font-black' : 'text-gray-500 font-medium'}`}>
//               <div className={`w-4 h-4 rounded-full mb-3 ${searchProgress.progress >= 100 ? 'bg-green-600' : 'bg-gray-300'}`}></div>
//               <span className="text-center">Complete</span>
//             </div>
//           </div>

//           {/* Tool Call Counter */}
//           {searchProgress.step !== "complete" && (
//             <div className="mt-8 text-center">
//               <span className="inline-flex items-center px-6 py-3 text-lg font-black bg-yellow text-dark" style={{ borderRadius: '20px' }}>
//                 🔧 Estimated tool calls: Up to 30
//               </span>
//             </div>
//           )}
//         </div>
//       )}

//       {/* Current Result */}
//       {currentResult && (
//         <div className="quasari-card p-10 border-2 border-gray-200">
//           <div className="flex items-center justify-between mb-8">
//             <h3 className="text-3xl font-black quasari-text">Latest Search Result</h3>
//             <div className="text-right">
//               <span className="text-base quasari-text-muted block font-bold">
//                 {new Date(currentResult.timestamp).toLocaleString()}
//               </span>
//               {(currentResult.toolCalls !== undefined || currentResult.sourcesScraped !== undefined) && (
//                 <div className="flex flex-wrap gap-3 mt-3">
//                   {currentResult.toolCalls !== undefined && (
//                     <span className="inline-flex items-center px-4 py-2 text-base font-black bg-yellow text-dark" style={{ borderRadius: '16px' }}>
//                       🔧 {currentResult.toolCalls} tool calls
//                     </span>
//                   )}
//                   {currentResult.sourcesScraped !== undefined && currentResult.sourcesScraped >= 0 && (
//                     <span className="inline-flex items-center px-4 py-2 text-base font-black bg-green-200 text-green-800" style={{ borderRadius: '16px' }}>
//                       📄 {currentResult.sourcesScraped} sources scraped
//                     </span>
//                   )}
//                   {currentResult.toolCalls !== undefined && currentResult.toolCalls >= 25 && (
//                     <span className="inline-flex items-center px-4 py-2 text-base font-black bg-purple-200 text-purple-800" style={{ borderRadius: '16px' }}>
//                       🚀 Comprehensive search
//                     </span>
//                   )}
//                   {currentResult.toolCalls !== undefined && currentResult.toolCalls >= 30 && (
//                     <span className="inline-flex items-center px-4 py-2 text-base font-black bg-yellow-300 text-dark" style={{ borderRadius: '16px' }}>
//                       ⭐ Maximum depth
//                     </span>
//                   )}
//                   {currentResult.complexity && (
//                     <span className={`inline-flex items-center px-4 py-2 text-base font-black ${
//                       currentResult.complexity === 'simple' ? 'bg-gray-200 text-gray-800' :
//                       currentResult.complexity === 'moderate' ? 'bg-orange-200 text-orange-800' :
//                       'bg-red-200 text-red-800'
//                     }`} style={{ borderRadius: '16px' }}>
//                       {currentResult.complexity === 'simple' ? '🔍' :
//                        currentResult.complexity === 'moderate' ? '🔎' : '🔬'} {currentResult.complexity}
//                     </span>
//                   )}
//                 </div>
//               )}
//             </div>
//           </div>
//           <div className="mb-8">
//             <span className="text-xl font-black quasari-text-muted">Query:</span>
//             <p className="quasari-text mt-3 text-2xl font-medium">{currentResult.query}</p>
//             {currentResult.reasoning && (
//               <p className="quasari-text-muted text-lg mt-3 italic font-medium">
//                 Analysis: {currentResult.reasoning}
//               </p>
//             )}
//           </div>
//           <div>
//             <span className="text-xl font-black quasari-text-muted">AI Response:</span>
//             <div className="mt-6 prose prose-xl max-w-none">
//               <div
//                 className="quasari-text formatted-response"
//                 dangerouslySetInnerHTML={{
//                   __html: formatResponseForDisplay(currentResult.response)
//                 }}
//               />
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Search History */}
//       {searches.length > 0 && (
//         <div className="space-y-8">
//           <h3 className="text-4xl font-black quasari-text">Search History</h3>
//           <div className="space-y-6">
//             {searches.map((search) => (
//               <div key={search._id} className="quasari-card p-8 border-2 border-gray-200 hover:border-yellow transition-all duration-200 hover:quasari-card-hover">
//                 <div className="flex items-center justify-between mb-6">
//                   <span className="text-base font-black quasari-text-muted bg-gray-200 px-4 py-2" style={{ borderRadius: '16px' }}>
//                     {new Date(search.timestamp).toLocaleString()}
//                   </span>
//                 </div>
//                 <div className="mb-6">
//                   <span className="text-xl font-black quasari-text-muted">Query:</span>
//                   <p className="quasari-text text-xl mt-3 font-medium">{search.query}</p>
//                 </div>
//                 <div>
//                   <span className="text-xl font-black quasari-text-muted">Response:</span>
//                   <p className="quasari-text-muted text-lg mt-3 line-clamp-3 font-medium">{search.aiResponse}</p>
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}

//       {searches.length === 0 && !currentResult && (
//         <div className="text-center py-20">
//           <div className="quasari-text-muted mb-8">
//             <svg className="mx-auto h-20 w-20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
//             </svg>
//           </div>
//           <h3 className="text-3xl font-black quasari-text mb-6">No searches yet</h3>
//           <p className="text-2xl quasari-text-muted font-medium">Start by asking a question or searching for anything!</p>
//         </div>
//       )}
//     </div>
//   );
// }
