import { createRoot } from "react-dom/client";
import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient } from "convex/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import "./index.css";
import App from "./App";
import "./lib/gsapSetup"; // Initialize GSAP

// Log page load and storage state
console.log('[PAGE LOAD] Application starting...', {
  url: window.location.href,
  timestamp: new Date().toISOString(),
  localStorage: {
    gmailOAuthInProgress: localStorage.getItem('gmail_oauth_in_progress'),
    gmailOAuthReturnPath: localStorage.getItem('gmail_oauth_return_path'),
    gmailOAuthUserId: localStorage.getItem('gmail_oauth_user_id'),
    gmailOAuthTimestamp: localStorage.getItem('gmail_oauth_timestamp'),
  },
  urlParams: Object.fromEntries(new URLSearchParams(window.location.search).entries())
});

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);

createRoot(document.getElementById("root")!).render(
  <BrowserRouter>
    <ConvexAuthProvider client={convex}>
      <App />
    </ConvexAuthProvider>
  </BrowserRouter>,
);
