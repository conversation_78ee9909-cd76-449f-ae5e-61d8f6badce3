import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { GmailSettings } from "./GmailSettings";
import { AnimatedIconButton } from "./components/AnimatedButton";
import { SidebarSessionTransition } from "./components/ConversationTransition";
import { useSidebarTransition } from "./hooks/useConversationTransition";

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onSelectSession?: (sessionId: string) => void;
  selectedSessionId?: string | null;
}

export function Sidebar({ isOpen, onToggle, isExpanded, onToggleExpanded, onSelectSession, selectedSessionId }: SidebarProps) {
  const sessions = useQuery(api.queries.getUserSessions) || [];
  const [activeTab, setActiveTab] = useState<'history' | 'settings' | 'ide'>('history');

  // Sidebar transition hooks
  const {
    handleSessionHover,
    handleSessionSelect,
    isSessionSelected,
    isSessionHovered,
  } = useSidebarTransition({
    sessions,
    selectedSessionId,
  });

  // Check if we should show settings tab (e.g., after OAuth return)
  useEffect(() => {
    const returnPath = localStorage.getItem('gmail_oauth_return_path');
    if (returnPath === '/settings') {
      setActiveTab('settings');
      localStorage.removeItem('gmail_oauth_return_path');
    }
  }, []);

  // Group sessions by date
  const groupedSessions = sessions.reduce((groups, session) => {
    const date = new Date(session.lastActivity);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    let groupKey: string;
    if (date.toDateString() === today.toDateString()) {
      groupKey = "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = "Yesterday";
    } else if (date.getTime() > today.getTime() - 7 * 24 * 60 * 60 * 1000) {
      groupKey = "Previous 7 days";
    } else {
      groupKey = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(session);
    return groups;
  }, {} as Record<string, typeof sessions>);

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-screen border-r border-gray-200 z-50 transition-all duration-300 ease-in-out flex flex-col
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        ${isExpanded ? 'w-80 lg:w-64' : 'w-80 lg:w-0'}
        lg:relative lg:translate-x-0
        ${!isExpanded ? 'lg:opacity-0 lg:pointer-events-none lg:overflow-hidden' : 'lg:opacity-100 lg:pointer-events-auto'}
      `} style={{ backgroundColor: '#FBF7F2' }}>
        {/* Header - Fixed */}
        <div className="flex-shrink-0" style={{ backgroundColor: '#FF9566', borderBottom: '1px solid #EDE4D8' }}>
          <div className="flex items-center justify-between p-3 h-16">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 flex items-center justify-center" style={{ backgroundColor: '#FF9566', borderRadius: '12px' }}>
                <span className="text-black font-black text-sm">Q</span>
              </div>
              {isExpanded && (
                <h2 className="text-sm text-black" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>Quasari</h2>
              )}
            </div>
            <div className="flex items-center gap-1">
              {/* Expand/Collapse Toggle - Desktop only */}
              <AnimatedIconButton
                onClick={onToggleExpanded}
                variant="ghost"
                size="sm"
                className="hidden lg:flex"
                ariaLabel={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
                icon={
                  <svg className="w-4 h-4" style={{ color: '#6F5A3E' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {isExpanded ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    )}
                  </svg>
                }
              />
              {/* Close Button - Mobile only */}
              <AnimatedIconButton
                onClick={onToggle}
                variant="ghost"
                size="sm"
                className="lg:hidden"
                ariaLabel="Close sidebar"
                icon={
                  <svg className="w-5 h-5" style={{ color: '#6F5A3E' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                }
              />
            </div>
          </div>

          {/* Tabs - Only show when expanded */}
          {isExpanded && (
            <div className="flex border-b border-gray-200">
              <button
                onClick={() => setActiveTab('history')}
                className={`flex-1 px-3 py-2 text-sm transition-colors ${
                  activeTab === 'history'
                    ? 'text-black border-b-2 border-black'
                    : 'hover:text-black'
                }`}
                style={{
                  fontFamily: 'Forum, serif',
                  fontWeight: '400',
                  color: activeTab === 'history' ? '#3D2914' : '#6F5A3E'
                }}
              >
                History
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`flex-1 px-3 py-2 text-sm transition-colors ${
                  activeTab === 'settings'
                    ? 'text-black border-b-2 border-black'
                    : 'hover:text-black'
                }`}
                style={{
                  fontFamily: 'Forum, serif',
                  fontWeight: '400',
                  color: activeTab === 'settings' ? '#3D2914' : '#6F5A3E'
                }}
              >
                Settings
              </button>
              <button
                onClick={() => setActiveTab('ide')}
                className={`flex-1 px-3 py-2 text-sm transition-colors ${
                  activeTab === 'ide'
                    ? 'text-black border-b-2 border-black'
                    : 'hover:text-black'
                }`}
                style={{
                  fontFamily: 'Forum, serif',
                  fontWeight: '400',
                  color: activeTab === 'ide' ? '#3D2914' : '#6F5A3E'
                }}
              >
                IDE
              </button>
            </div>
          )}

          {/* Collapsed Tab Icons - Only show when collapsed */}
          {!isExpanded && (
            <div className="flex flex-col border-b border-gray-200">
              <button
                onClick={() => setActiveTab('history')}
                className={`p-3 transition-colors ${
                  activeTab === 'history'
                    ? 'bg-black/10'
                    : 'hover:bg-black/5'
                }`}
                title="History"
              >
                <svg className="w-5 h-5 mx-auto" style={{ color: activeTab === 'history' ? '#3D2914' : '#6F5A3E' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`p-3 transition-colors ${
                  activeTab === 'settings'
                    ? 'bg-black/10'
                    : 'hover:bg-black/5'
                }`}
                title="Settings"
              >
                <svg className="w-5 h-5 mx-auto" style={{ color: activeTab === 'settings' ? '#3D2914' : '#6F5A3E' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
              <button
                onClick={() => setActiveTab('ide')}
                className={`p-3 transition-colors ${
                  activeTab === 'ide'
                    ? 'bg-black/10'
                    : 'hover:bg-black/5'
                }`}
                title="IDE"
              >
                <svg className="w-5 h-5 mx-auto" style={{ color: activeTab === 'ide' ? '#3D2914' : '#6F5A3E' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l-4 4-4-4 4-4" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Content Area - Only show when expanded */}
        {isExpanded && (
          <>
            {/* Content Area - Scrollable */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden sidebar-scroll backdrop-blur-sm" style={{ backgroundColor: 'rgba(254, 252, 250, 0.9)' }}>
              {activeTab === 'history' ? (
                <div className="p-3">
                  {sessions.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="mb-3" style={{ color: '#A68E6F' }}>
                        <svg className="mx-auto h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <p className="text-sm" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#6F5A3E' }}>No conversations yet</p>
                      <p className="text-xs mt-1" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#A68E6F' }}>Start a conversation to see history</p>
                    </div>
                  ) : (
                    // Expanded view - Full session details
                    <div className="space-y-4">
                      {Object.entries(groupedSessions).map(([groupName, groupSessions]) => (
                        <div key={groupName}>
                          <h3 className="text-xs uppercase tracking-wide mb-2" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#A68E6F' }}>
                            {groupName}
                          </h3>
                          <div className="space-y-1">
                            {groupSessions.map((session) => (
                              <SidebarSessionTransition
                                key={session._id}
                                sessionId={session.sessionId}
                                isSelected={isSessionSelected(session.sessionId)}
                                isHovered={isSessionHovered(session.sessionId)}
                                className="w-full"
                              >
                                <button
                                  onClick={() => {
                                    handleSessionSelect(session.sessionId);
                                    onSelectSession?.(session.sessionId);
                                  }}
                                  onMouseEnter={() => handleSessionHover(session.sessionId)}
                                  onMouseLeave={() => handleSessionHover(null)}
                                  className="w-full text-left p-2 transition-all duration-200 group border border-gray-200 hover:opacity-90"
                                  style={{
                                    borderRadius: '12px',
                                    backgroundColor: '#FFF8F3', // Very light warm orange
                                  }}
                                >
                                <div className="space-y-1">
                                  <p className="text-xs text-black leading-tight" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                                    {truncateText(session.title || 'Untitled Conversation', 40)}
                                  </p>
                                  <p className="text-xs leading-tight" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#A68E6F' }}>
                                    {session.messageCount} message{session.messageCount !== 1 ? 's' : ''}
                                  </p>
                                  <div className="flex items-center justify-between">
                                    <span className="text-xs" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#BFA88C' }}>
                                      {new Date(session.lastActivity).toLocaleTimeString('en-US', {
                                        hour: 'numeric',
                                        minute: '2-digit',
                                        hour12: true
                                      })}
                                    </span>
                                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                      <svg className="w-3 h-3" style={{ color: '#BFA88C' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                      </svg>
                                    </div>
                                  </div>
                                </div>
                                </button>
                              </SidebarSessionTransition>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : activeTab === 'settings' ? (
                <div className="p-3">
                  <GmailSettings />
                </div>
              ) : (
                <div className="p-3">
                  <Link to="/ide" className="block w-full text-left p-2 transition-all duration-200 group border border-gray-200 hover:opacity-90" style={{ borderRadius: '12px', backgroundColor: '#FFF8F3' }}>
                    <div className="space-y-1">
                      <p className="text-xs text-black leading-tight" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                        Open IDE
                      </p>
                    </div>
                  </Link>
                </div>
              )}
            </div>

            {/* Footer - Fixed */}
            <div className="flex-shrink-0 p-3 backdrop-blur-sm" style={{ backgroundColor: 'rgba(254, 252, 250, 0.9)', borderTop: '1px solid #EDE4D8' }}>
              <div className="text-center">
                <p className="text-xs" style={{ fontFamily: 'Forum, serif', fontWeight: '400', color: '#A68E6F' }}>
                  {sessions.length} conversation{sessions.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Toggle Button (when sidebar is closed) */}
      {!isOpen && (
        <AnimatedIconButton
          onClick={onToggle}
          variant="filled"
          size="md"
          className="fixed top-4 left-4 z-30 lg:hidden"
          style={{ backgroundColor: '#FBF7F2', borderColor: '#EDE4D8' }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#F5F0E8'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#FBF7F2'}
          ariaLabel="Open sidebar"
          icon={
            <svg className="w-5 h-5" style={{ color: '#6F5A3E' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          }
        />
      )}
    </>
  );
}
