import { useState, useEffect, useRef } from "react";
import { useAction, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { toast } from "sonner";
import "./formatted-response.css";
import { AnimatedSpinner, AnimatedPlusIcon } from "./components/AnimatedIcons";
import { AnimatedProgressBar } from "./components/AnimatedProgressBar";
import { AnimatedButton } from "./components/AnimatedButton";
import {
  ConversationTransition,
  MessageListTransition,
  MessageBubbleTransition,
  LoadingTransition
} from "./components/ConversationTransition";
import { useConversationTransition } from "./hooks/useConversationTransition";

// Function to format response for HTML display
function formatResponseForDisplay(response: string): string {
  if (!response || typeof response !== 'string') {
    return '';
  }
  let formatted = response;

  // First, handle JSON code blocks
  formatted = formatted.replace(/```json\n?([\s\S]*?)\n?```/g, (match, jsonContent) => {
    try {
      // Try to parse and format the JSON
      const parsed = JSON.parse(jsonContent.trim());
      const prettyJson = JSON.stringify(parsed, null, 2);

      // Add syntax highlighting to JSON
      const highlightedJson = prettyJson
        .replace(/(".*?")\s*:/g, '<span class="json-key">$1</span>:')
        .replace(/:\s*(".*?")/g, ': <span class="json-string">$1</span>')
        .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
        .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>')
        .replace(/:\s*(\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
        .replace(/([{}[\]])/g, '<span class="json-bracket">$1</span>');

      const copyId = `json-${Math.random().toString(36).substring(2, 11)}`;
      return `<div class="json-block">
        <div class="json-header">
          <span>📄 JSON Response</span>
        </div>
        <button class="copy-button" onclick="navigator.clipboard.writeText(document.getElementById('${copyId}').textContent); this.textContent='✅ Copied!'; setTimeout(() => this.textContent='📋 Copy', 2000)">📋 Copy</button>
        <pre class="json-content"><code id="${copyId}" style="display:none;">${prettyJson}</code><div class="json-highlighted">${highlightedJson}</div></pre>
      </div>`;
    } catch (e) {
      // If parsing fails, just show as code block
      const copyId = `code-${Math.random().toString(36).substring(2, 11)}`;
      return `<div class="code-block">
        <div class="code-header">
          <span>📄 JSON</span>
        </div>
        <button class="copy-button" onclick="navigator.clipboard.writeText(document.getElementById('${copyId}').textContent); this.textContent='✅ Copied!'; setTimeout(() => this.textContent='📋 Copy', 2000)">📋 Copy</button>
        <pre class="code-content"><code id="${copyId}">${jsonContent.trim()}</code></pre>
      </div>`;
    }
  });

  // Handle other code blocks
  formatted = formatted.replace(/```(\w+)?\n?([\s\S]*?)\n?```/g, (match, language, codeContent) => {
    const lang = language || 'text';
    const copyId = `code-${Math.random().toString(36).substring(2, 11)}`;
    return `<div class="code-block">
      <div class="code-header">
        ${lang.toUpperCase()}
      </div>
      <button class="copy-button" onclick="navigator.clipboard.writeText(document.getElementById('${copyId}').textContent); this.textContent='Copied!'; setTimeout(() => this.textContent='Copy', 2000)">Copy</button>
      <pre class="code-content"><code id="${copyId}">${codeContent.trim()}</code></pre>
    </div>`;
  });

  // Convert **text** to <strong>text</strong>
  formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Convert *text* to <em>text</em>
  formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Convert line breaks to <br> tags
  formatted = formatted.replace(/\n/g, '<br>');

  // Convert URLs to clickable links
  formatted = formatted.replace(
    /(https?:\/\/[^\s<>"{}|\\^`[\]]+)/g,
    '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
  );

  // Convert numbered lists
  formatted = formatted.replace(/^\d+\.\s(.+)$/gm, '<li>$1</li>');
  formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ol>$1</ol>');

  // Convert bullet points
  formatted = formatted.replace(/^[-•]\s(.+)$/gm, '<li>$1</li>');
  formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

  // Convert headers (## Header)
  formatted = formatted.replace(/^##\s(.+)$/gm, '<h3>$1</h3>');
  formatted = formatted.replace(/^#\s(.+)$/gm, '<h2>$1</h2>');

  return formatted;
}

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'progress';
  content: string;
  timestamp: number;
  toolCalls?: number;
  sourcesScraped?: number;
  complexity?: 'simple' | 'moderate' | 'complex';
  reasoning?: string;
  progress?: {
    step: string;
    progress: number;
    message: string;
    sourceUrl?: string;
    sourceTitle?: string;
  };
}

interface ChatInterfaceProps {
  hasSearchHistory: boolean;
  selectedSessionId?: string | null;
}

export function ChatInterface({ hasSearchHistory, selectedSessionId }: ChatInterfaceProps) {
  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [currentSearchId, setCurrentSearchId] = useState<string | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [completedSearchIds, setCompletedSearchIds] = useState<Set<string>>(new Set());
  const [showCompletionAnimation, setShowCompletionAnimation] = useState(false);
  const [isNewSessionExplicit, setIsNewSessionExplicit] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Conversation transition state
  const {
    isTransitioning,
    isLoading: isTransitionLoading,
    isMessageNew,
    setLoading: setTransitionLoading,
  } = useConversationTransition({
    selectedSessionId: selectedSessionId ?? null,
    messages,
    onTransitionStart: () => {
      console.log('[TRANSITION] Conversation transition started');
    },
    onTransitionEnd: () => {
      console.log('[TRANSITION] Conversation transition completed');
    },
  });



  const searchWebWithProgress = useAction(api.search.searchWebWithProgress);
  const sessions = useQuery(api.queries.getUserSessions) || [];
  const selectedSession = useQuery(
    api.queries.getSession,
    selectedSessionId ? { sessionId: selectedSessionId } : "skip"
  );
  const searchProgress = useQuery(
    api.queries.getSearchProgress,
    currentSearchId ? { searchId: currentSearchId } : "skip"
  );

  // Reset search state on component mount - comprehensive cleanup
  useEffect(() => {
    // Always reset on mount to prevent stale state
    setIsSearching(false);
    setCurrentSearchId(null);
    setCompletedSearchIds(new Set());
    setShowCompletionAnimation(false);
  }, []); // Only run on mount

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load session conversation history when a session is selected
  useEffect(() => {
    if (selectedSessionId && selectedSession) {
      // Clear existing messages when switching to a different session
      if (currentSessionId && currentSessionId !== selectedSessionId) {
        setTransitionLoading(true);
        setMessages([]);
        // Also clear any search progress state when switching sessions
        setCurrentSearchId(null);
        setIsSearching(false);
        setShowCompletionAnimation(false);
        setCompletedSearchIds(new Set());
      }

      try {
        const conversationHistory = JSON.parse(selectedSession.conversationHistory || '[]');

        if (Array.isArray(conversationHistory)) {
          const sessionMessages: Message[] = conversationHistory
            .filter((msg: any) => {
              // Handle both formats: {role, content} and {role, parts}
              const hasContent = msg && (
                (msg.content && typeof msg.content === 'string') ||
                (msg.parts && Array.isArray(msg.parts) && msg.parts[0]?.text)
              );
              return hasContent;
            })
            .map((msg: any, index: number) => {
              // Extract content from either format
              let content = '';
              if (msg.content) {
                content = msg.content;
              } else if (msg.parts && Array.isArray(msg.parts) && msg.parts[0]?.text) {
                content = msg.parts[0].text;
              }

              return {
                id: `session-${selectedSessionId}-${index}`,
                type: msg.role === 'user' ? 'user' : 'assistant',
                content: content,
                timestamp: selectedSession.lastActivity + index,
              };
            });
          setMessages(sessionMessages);
          setCurrentSessionId(selectedSessionId);
          setTransitionLoading(false);
        } else {
          console.warn("Conversation history is not an array:", conversationHistory);
          setMessages([]);
          setCurrentSessionId(selectedSessionId);
          setTransitionLoading(false);
        }
      } catch (error) {
        console.error("Error parsing session conversation history:", error);
        setMessages([]);
        setCurrentSessionId(selectedSessionId);
        setTransitionLoading(false);
      }
    } else if (!selectedSessionId && sessions.length > 0 && messages.length === 0 && !isNewSessionExplicit) {
      // Only load the most recent session if no specific session is selected AND no messages are loaded AND not explicitly starting new session
      const mostRecentSession = sessions[0]; // sessions are already sorted by lastActivity desc
      if (mostRecentSession && mostRecentSession.conversationHistory) {
        try {
          const conversationHistory = JSON.parse(mostRecentSession.conversationHistory || '[]');
          if (Array.isArray(conversationHistory)) {
            const sessionMessages: Message[] = conversationHistory
              .filter((msg: any) => {
                // Handle both formats: {role, content} and {role, parts}
                const hasContent = msg && (
                  (msg.content && typeof msg.content === 'string') ||
                  (msg.parts && Array.isArray(msg.parts) && msg.parts[0]?.text)
                );
                return hasContent;
              })
              .map((msg: any, index: number) => {
                // Extract content from either format
                let content = '';
                if (msg.content) {
                  content = msg.content;
                } else if (msg.parts && Array.isArray(msg.parts) && msg.parts[0]?.text) {
                  content = msg.parts[0].text;
                }

                return {
                  id: `recent-${mostRecentSession.sessionId}-${index}`,
                  type: msg.role === 'user' ? 'user' : 'assistant',
                  content: content,
                  timestamp: mostRecentSession.lastActivity + index,
                };
              });
            setMessages(sessionMessages);
            setCurrentSessionId(mostRecentSession.sessionId);
          }
        } catch (error) {
          console.error("Error parsing recent session conversation history:", error);
        }
      }
    }
  }, [selectedSessionId, selectedSession, sessions, isNewSessionExplicit]);

  // Handle search progress updates and completion
  useEffect(() => {
    if (searchProgress && (isSearching || showCompletionAnimation)) {
      setMessages(prev => {
        const filtered = prev.filter(m => m.type !== 'progress');
        return [...filtered, {
          id: `progress-${currentSearchId}`,
          type: 'progress',
          content: searchProgress.message,
          timestamp: Date.now(),
          progress: searchProgress
        }];
      });

      // Enhanced completion handling with duplicate prevention
      if (searchProgress.step === "complete" &&
          isSearching &&
          currentSearchId &&
          searchProgress.searchId === currentSearchId &&
          !completedSearchIds.has(currentSearchId)) {

        // Mark this search as completed to prevent duplicates
        setCompletedSearchIds(prev => new Set(prev).add(currentSearchId));

        // Check if we have a final result in the progress
        const finalResult = (searchProgress as any)?.finalResult;
        if (finalResult) {
          // Update session ID if this was a new session and we don't already have one
          if (!currentSessionId && finalResult.sessionId) {
            setCurrentSessionId(finalResult.sessionId);
            console.log(`[UI] Updated session ID from search result: ${finalResult.sessionId}`);
          }

          // Add assistant response
          const assistantMessage: Message = {
            id: `assistant-${Date.now()}`,
            type: 'assistant',
            content: finalResult.response,
            timestamp: Date.now(),
            toolCalls: finalResult.toolCalls,
            sourcesScraped: finalResult.sourcesScraped,
          };

          setMessages(prev => {
            const filtered = prev.filter(m => m.type !== 'progress');
            // Check if this response already exists to prevent duplicates
            const exists = filtered.some(m =>
              m.type === 'assistant' &&
              m.content === assistantMessage.content &&
              Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // Within 5 seconds
            );
            if (exists) {
              return filtered;
            }
            return filtered.concat([assistantMessage]);
          });
          toast.success(`Search completed! Used ${finalResult.toolCalls || 1} tool calls and scraped ${finalResult.sourcesScraped || 0} sources.`);
        }

        // Show completion animation briefly, then reset
        setShowCompletionAnimation(true);

        // Immediate state reset for responsive UI
        setIsSearching(false);
        setCurrentSearchId(null);

        // Hide completion animation after a short delay
        const animationTimer = setTimeout(() => {
          setShowCompletionAnimation(false);
        }, 1500);
        return () => clearTimeout(animationTimer);
      }
    }
  }, [searchProgress?.step, searchProgress?.searchId, searchProgress, isSearching, currentSearchId, currentSessionId, completedSearchIds]);

  // Clean up progress messages when search is no longer active and animation is done
  useEffect(() => {
    if (!isSearching && !showCompletionAnimation) {
      // Remove any lingering progress messages when search stops and animation is complete
      setMessages(prev => prev.filter(m => m.type !== 'progress'));
    }
  }, [isSearching, showCompletionAnimation]);

  // Clean up progress messages when session changes or when starting new session
  useEffect(() => {
    if (isNewSessionExplicit || (!currentSearchId && !isSearching)) {
      // Remove progress messages when explicitly starting new session or when no active search
      setMessages(prev => prev.filter(m => m.type !== 'progress'));
    }
  }, [isNewSessionExplicit, currentSearchId, isSearching]);

  // Function to start a new conversation session
  const startNewSession = () => {
    console.log(`[UI] Starting new session, clearing currentSessionId: ${currentSessionId}`);

    // Clear all session and search state
    setCurrentSessionId(null);
    setMessages([]);

    // Clear search progress state to prevent stale progress from appearing
    setCurrentSearchId(null);
    setIsSearching(false);
    setShowCompletionAnimation(false);
    setCompletedSearchIds(new Set());

    // Mark that this is an explicit new session to prevent auto-loading
    setIsNewSessionExplicit(true);

    toast.success("Started new conversation session!");
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    const trimmedQuery = query.trim();

    // Handle special commands
    if (trimmedQuery === '/new' || trimmedQuery === '/clear') {
      startNewSession();
      setQuery("");
      return;
    }

    if (trimmedQuery === '/session') {
      if (currentSessionId) {
        toast.info(`Current session: ${currentSessionId}`);
      } else {
        toast.info("No active session. Next search will start a new session.");
      }
      setQuery("");
      return;
    }

    if (trimmedQuery.length > 3000) {
      toast.error("Query too long! Please keep your question under 3000 characters.");
      return;
    }

    if (trimmedQuery.length < 3) {
      toast.error("Query too short! Please enter at least 3 characters.");
      return;
    }

    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: trimmedQuery,
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setQuery("");

    const searchId = `search_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    setCurrentSearchId(searchId);
    setIsSearching(true);
    setShowCompletionAnimation(false);
    // Clear any previous completion tracking for this new search
    setCompletedSearchIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(searchId); // Ensure this new ID is not marked as completed
      return newSet;
    });

    // If no current session, generate a new session ID immediately
    let searchSessionId = currentSessionId;
    if (!currentSessionId) {
      searchSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setCurrentSessionId(searchSessionId);
      console.log(`[UI] Created new session ID: ${searchSessionId}`);
    }

    // Reset the explicit new session flag since we're now starting a search
    setIsNewSessionExplicit(false);

    try {
      console.log(`[UI] Sending search with sessionId: ${searchSessionId}, searchId: ${searchId}`);
      const result = await searchWebWithProgress({
        query: trimmedQuery,
        searchId,
        sessionId: searchSessionId || undefined
      });

      // Check if search started successfully
      if (result.status === "started") {
        toast.success("Search started! Check progress above for updates.");
        // The search will continue in background, progress will be tracked via searchProgress
        // We'll handle the completion in the useEffect below
      } else {
        // Handle legacy response format (shouldn't happen with new implementation)
        const searchResult = result as any;

        // Update session ID if this was a new session and we don't already have one
        if (!currentSessionId && searchResult.sessionId) {
          setCurrentSessionId(searchResult.sessionId);
          console.log(`[UI] Updated session ID from legacy result: ${searchResult.sessionId}`);
        }

        // Add assistant response
        const assistantMessage: Message = {
          id: `assistant-${Date.now()}`,
          type: 'assistant',
          content: searchResult.response,
          timestamp: Date.now(),
          toolCalls: searchResult.toolCalls,
          sourcesScraped: searchResult.sourcesScraped,
          complexity: searchResult.complexity,
          reasoning: searchResult.reasoning,
        };

        setMessages(prev => {
          const filtered = prev.filter(m => m.type !== 'progress');
          // Check if this response already exists to prevent duplicates
          const exists = filtered.some(m =>
            m.type === 'assistant' &&
            m.content === assistantMessage.content &&
            Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // Within 5 seconds
          );
          if (exists) {
            return filtered;
          }
          return filtered.concat([assistantMessage]);
        });
        setIsSearching(false);
        setCurrentSearchId(null);
        toast.success(`Search completed! Used ${searchResult.toolCalls || 1} tool calls and scraped ${searchResult.sourcesScraped || 0} sources.`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Search failed. Please try again.";
      toast.error(errorMessage);
      console.error("Search error:", error);

      // Clean up search state immediately on error
      setIsSearching(false);
      setCurrentSearchId(null);

      // Add error message
      const errorMsg: Message = {
        id: `error-${Date.now()}`,
        type: 'assistant',
        content: `Sorry, I encountered an error: ${errorMessage}`,
        timestamp: Date.now(),
      };
      setMessages(prev => prev.filter(m => m.type !== 'progress').concat([errorMsg]));
    }
  };

  return (
    <div className="relative h-screen">
      {/* Loading Transition Overlay */}
      <LoadingTransition
        isVisible={isTransitionLoading}
        message="Loading conversation..."
      />

      {/* Messages Area - Full Height with Top and Bottom Padding */}
      <div className="h-full overflow-y-auto overflow-x-hidden px-4 py-6 chat-scroll" style={{ backgroundColor: 'rgba(255, 255, 255, 0.3)', paddingTop: '80px', paddingBottom: '140px' }}>
        <ConversationTransition
          sessionId={selectedSessionId ?? null}
          isLoading={isTransitionLoading}
          className="max-w-4xl mx-auto space-y-6"
        >
          {/* New Chat Button - Show when there are sessions and no specific session is selected */}
          {!selectedSessionId && sessions.length > 0 && (
            <div className="flex justify-center mb-6">
              <AnimatedButton
                onClick={startNewSession}
                variant="secondary"
                size="md"
                icon={<AnimatedPlusIcon isOpen={false} size={20} />}
                iconPosition="left"
              >
                Start New Conversation
              </AnimatedButton>
            </div>
          )}

          {messages.length === 0 && !hasSearchHistory && (
            <div className="text-center py-20">
              <h1 className="quasari-heading text-3xl md:text-4xl mb-6 max-w-2xl mx-auto leading-tight">
                THE EASIEST WAY<br />
                TO <span className="relative inline-block">
                  SEARCH
                  <div className="absolute -bottom-1 left-0 right-0 h-2 md:h-3" style={{ backgroundColor: '#FFCBA8' }}></div>
                </span> THE WEB
              </h1>
              <p className="text-lg mb-8 max-w-xl mx-auto font-normal" style={{ color: '#6F5A3E' }}>
                Ask me anything and I'll search the web with AI-powered insights.<br />
                <span className="text-sm mt-2 block" style={{ color: '#A68E6F' }}>I remember our conversation context for follow-up questions!</span>
              </p>
            </div>
          )}

          <MessageListTransition
            sessionId={selectedSessionId ?? null}
            className="space-y-6"
          >
            {messages.map((message) => (
              <MessageBubbleTransition
                key={message.id}
                messageId={message.id}
                messageType={message.type}
                isNew={isMessageNew(message.id)}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
              >
              {message.type === 'user' ? (
                <div className="max-w-2xl">
                  <div className="chat-message-user px-4 py-3 text-base" style={{ fontFamily: 'Forum, serif', fontWeight: '400', letterSpacing: '-0.01em' }}>
                    {message.content || ''}
                  </div>
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {new Date(message.timestamp).toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </div>
                </div>
              ) : message.type === 'progress' ? (
                <div className="max-w-4xl w-full">
                  <div className={`quasari-card p-10 border-2 transition-all duration-500 ${
                    message.progress?.step === "complete" ? 'border-green-500 bg-green-50' : ''
                  }`} style={{ borderColor: message.progress?.step === "complete" ? '#10B981' : '#FF9566' }}>
                    <div className="flex items-center justify-between mb-8">
                      <h3 className={`text-2xl quasari-text ${
                        message.progress?.step === "complete" ? 'text-green-800' : ''
                      }`} style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                        {message.progress?.step === "complete" ? 'Search Complete!' : 'Search in Progress'}
                      </h3>
                      <span className={`text-xl font-black text-dark px-6 py-3 ${
                        message.progress?.step === "complete" ? 'bg-green-500 text-white' : ''
                      }`} style={{
                        borderRadius: '20px',
                        backgroundColor: message.progress?.step === "complete" ? '#10B981' : '#FF9566'
                      }}>
                        {message.progress?.progress}%
                      </span>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-8">
                      <AnimatedProgressBar
                        progress={message.progress?.progress || 0}
                        height={24}
                        color="#FF9566"
                        backgroundColor="#E5E7EB"
                        showPercentage={false}
                        animated={true}
                        duration={0.5}
                      />
                    </div>

                    {/* Current Step */}
                    <div className="flex items-center gap-6">
                      <div className="flex-shrink-0">
                        {message.progress?.step === "searching" && (
                          <AnimatedSpinner size={32} color="#000000" speed={1.2} />
                        )}
                        {message.progress?.step === "analyzing" && (
                          <div className="animate-pulse">
                            <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                          </div>
                        )}
                        {message.progress?.step === "scraping" && (
                          <div className="animate-bounce">
                            <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                        )}
                        {message.progress?.step === "processing" && (
                          <div className="animate-pulse">
                            <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                          </div>
                        )}
                        {message.progress?.step === "finalizing" && (
                          <div className="animate-spin">
                            <svg className="h-8 w-8 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                          </div>
                        )}
                        {message.progress?.step === "complete" && (
                          <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {message.progress?.step === "error" && (
                          <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="quasari-text text-2xl capitalize" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                          {message.progress?.step?.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="quasari-text-muted text-lg" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                          {message.content}
                        </p>
                        {/* Show source information when scraping */}
                        {message.progress?.step === "scraping" && message.progress?.sourceUrl && (
                          <div className="mt-6 p-6 bg-white border-2 border-gray-200 shadow-sm" style={{ borderRadius: '20px' }}>
                            <div className="flex items-start gap-4">
                              <div className="flex-shrink-0 mt-1">
                                <svg className="h-6 w-6 text-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="quasari-text text-base leading-tight" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}>
                                  {message.progress?.sourceTitle || "Unknown Title"}
                                </p>
                                <p className="quasari-text-muted text-sm mt-2 truncate" style={{ fontFamily: 'Forum, serif', fontWeight: '400' }} title={message.progress?.sourceUrl}>
                                  {(() => {
                                    try {
                                      return new URL(message.progress?.sourceUrl || '').hostname;
                                    } catch {
                                      return message.progress?.sourceUrl;
                                    }
                                  })()}
                                </p>
                              </div>
                              <div className="flex-shrink-0">
                                <AnimatedSpinner size={20} color="#000000" speed={1.5} />
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Step Indicators */}
                    <div className="mt-10 grid grid-cols-6 gap-4 text-base" style={{ fontFamily: 'Forum, serif' }}>
                      <div className={`flex flex-col items-center ${(message.progress?.progress || 0) >= 10 ? 'text-dark' : 'text-gray-500'}`} style={{ fontWeight: '400' }}>
                        <div className={`w-4 h-4 rounded-full mb-3`} style={{ backgroundColor: (message.progress?.progress || 0) >= 10 ? '#FF9566' : '#EDE4D8' }}></div>
                        <span className="text-center">Search</span>
                      </div>
                      <div className={`flex flex-col items-center ${(message.progress?.progress || 0) >= 30 ? 'text-dark' : 'text-gray-500'}`} style={{ fontWeight: '400' }}>
                        <div className={`w-4 h-4 rounded-full mb-3`} style={{ backgroundColor: (message.progress?.progress || 0) >= 30 ? '#FF9566' : '#EDE4D8' }}></div>
                        <span className="text-center">Analyze</span>
                      </div>
                      <div className={`flex flex-col items-center ${(message.progress?.progress || 0) >= 40 ? 'text-dark' : 'text-gray-500'}`} style={{ fontWeight: '400' }}>
                        <div className={`w-4 h-4 rounded-full mb-3`} style={{ backgroundColor: (message.progress?.progress || 0) >= 40 ? '#FF9566' : '#EDE4D8' }}></div>
                        <span className="text-center">Scrape</span>
                      </div>
                      <div className={`flex flex-col items-center ${(message.progress?.progress || 0) >= 80 ? 'text-dark' : 'text-gray-500'}`} style={{ fontWeight: '400' }}>
                        <div className={`w-4 h-4 rounded-full mb-3`} style={{ backgroundColor: (message.progress?.progress || 0) >= 80 ? '#FF9566' : '#EDE4D8' }}></div>
                        <span className="text-center">Research</span>
                      </div>
                      <div className={`flex flex-col items-center ${(message.progress?.progress || 0) >= 90 ? 'text-dark' : 'text-gray-500'}`} style={{ fontWeight: '400' }}>
                        <div className={`w-4 h-4 rounded-full mb-3`} style={{ backgroundColor: (message.progress?.progress || 0) >= 90 ? '#FF9566' : '#EDE4D8' }}></div>
                        <span className="text-center">Process</span>
                      </div>
                      <div className={`flex flex-col items-center ${(message.progress?.progress || 0) >= 100 ? 'text-green-800' : 'text-gray-500'}`} style={{ fontWeight: '400' }}>
                        <div className={`w-4 h-4 rounded-full mb-3 ${(message.progress?.progress || 0) >= 100 ? 'bg-green-600' : 'bg-gray-300'}`}></div>
                        <span className="text-center">Complete</span>
                      </div>
                    </div>

                    {/* Tool Call Counter */}
                    {message.progress?.step !== "complete" && (
                      <div className="mt-8 text-center">
                        <span className="inline-flex items-center px-6 py-3 text-lg text-dark" style={{ borderRadius: '20px', backgroundColor: '#FFB088', fontFamily: 'Forum, serif', fontWeight: '400' }}>
                          🔧 Estimated tool calls: Up to 30
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="max-w-3xl">
                  <div className="chat-message-assistant p-4">
                    <div
                      className="quasari-text formatted-response text-base"
                      style={{ fontFamily: 'Forum, serif', fontWeight: '400' }}
                      dangerouslySetInnerHTML={{
                        __html: formatResponseForDisplay(message.content || '')
                      }}
                    />
                    {(message.toolCalls !== undefined || message.sourcesScraped !== undefined) && (
                      <div className="flex flex-wrap gap-2 mt-3 pt-3 border-t border-gray-200">
                        {message.toolCalls !== undefined && (
                          <span className="inline-flex items-center px-2 py-1 text-xs text-black" style={{ borderRadius: '12px', backgroundColor: '#FFB088', fontFamily: 'Forum, serif', fontWeight: '400' }}>
                            🔧 {message.toolCalls} tool calls
                          </span>
                        )}
                        {message.sourcesScraped !== undefined && message.sourcesScraped >= 0 && (
                          <span className="inline-flex items-center px-2 py-1 text-xs bg-green-200 text-green-800" style={{ borderRadius: '12px', fontFamily: 'Forum, serif', fontWeight: '400' }}>
                            📄 {message.sourcesScraped} sources
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(message.timestamp).toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </div>
                </div>
              )}
              </MessageBubbleTransition>
            ))}
          </MessageListTransition>
          <div ref={messagesEndRef} />
        </ConversationTransition>
      </div>

      {/* Input Area - Fixed Overlay at Bottom */}
      <div className="absolute bottom-0 left-0 right-0 backdrop-blur-md" style={{ borderTop: '1px solid #EDE4D8', backgroundColor: 'rgba(255, 255, 255, 0.8)' }}>
        <div className="max-w-4xl mx-auto px-4 py-3">
          {/* Session Info and Controls */}
          {currentSessionId && (
            <div className="flex items-center justify-between mb-3 text-sm">
              <div className="flex items-center gap-2" style={{ color: '#6F5A3E' }}>
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <span>Continuing conversation</span>
              </div>
              <AnimatedButton
                onClick={startNewSession}
                variant="ghost"
                size="sm"
                className="text-xs border border-orange-200 hover:border-orange-300"
                style={{ color: '#6F5A3E' }}
              >
                New Chat
              </AnimatedButton>
            </div>
          )}
          <form onSubmit={handleSearch} className="flex gap-3 items-end">
            <div className="flex-1 relative">
              <textarea
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder={currentSessionId ? "Continue the conversation..." : "Ask me anything... (Type /new for new chat)"}
                className={`w-full px-4 py-3 backdrop-blur-md border-2 resize-none focus:ring-2 outline-none transition-all duration-200 placeholder-gray-500 text-base ${
                  query.length > 3000
                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
                    : query.length > 2500
                    ? 'border-yellow-500 focus:border-yellow-500 focus:ring-yellow-500/20'
                    : 'focus:ring-orange-200/50'
                }`}
                style={{
                  borderRadius: '24px',
                  letterSpacing: '-0.01em',
                  fontFamily: 'Forum, serif',
                  fontWeight: '400',
                  color: '#3D2914',
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  borderColor: '#EDE4D8'
                }}
                onFocus={(e) => e.currentTarget.style.borderColor = '#FF9566'}
                onBlur={(e) => e.currentTarget.style.borderColor = '#EDE4D8'}
                disabled={isSearching}
                rows={query.length > 100 ? (query.length > 500 ? 3 : 2) : 1}
                maxLength={3100}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSearch(e);
                  }
                }}
              />
              {query.length > 0 && (
                <div className={`absolute bottom-2 right-2 text-xs px-2 py-1 ${
                  query.length > 3000
                    ? 'bg-red-100 text-red-700'
                    : query.length > 2500
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
                style={{ borderRadius: '12px', fontFamily: 'Forum, serif', fontWeight: '400' }}>
                  {query.length}/3000
                </div>
              )}
            </div>
            <AnimatedButton
              type="submit"
              disabled={isSearching || !query.trim() || query.length > 3000 || query.length < 3}
              variant="orange"
              size="md"
              loading={isSearching}
              icon={isSearching ? <AnimatedSpinner size={16} color="#000000" speed={1.5} /> : undefined}
              iconPosition="left"
              className="whitespace-nowrap min-h-[48px]"
            >
              {isSearching ? (
                <span className="hidden sm:inline">Searching...</span>
              ) : (
                "Search"
              )}
            </AnimatedButton>
          </form>
        </div>
      </div>
    </div>
  );
}