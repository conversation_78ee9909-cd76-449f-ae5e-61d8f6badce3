import React, { useState, useEffect } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { okaidia } from '@uiw/codemirror-theme-okaidia';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';

const IDE = () => {
  const files = useQuery(api.files.getFiles) || [];
  const createFile = useMutation(api.files.createFile);
  const updateFile = useMutation(api.files.updateFile);
  const deleteFile = useMutation(api.files.deleteFile);

  const [selectedFile, setSelectedFile] = useState(null);
  const [newFileName, setNewFileName] = useState('');

  useEffect(() => {
    if (files.length > 0 && !selectedFile) {
      setSelectedFile(files[0]);
    }
  }, [files, selectedFile]);

  const handleCreateFile = async () => {
    if (newFileName) {
      await createFile({ name: newFileName, content: '' });
      setNewFileName('');
    }
  };

  const handleDeleteFile = async (fileId) => {
    await deleteFile({ id: fileId });
    if (selectedFile?._id === fileId) {
      setSelectedFile(null);
    }
  };

  const handleContentChange = (value) => {
    if (selectedFile) {
      updateFile({ id: selectedFile._id, content: value });
    }
  };

  return (
    <div className="flex h-screen bg-gray-900 text-white">
      <div className="w-1/4 border-r border-gray-700 p-4">
        <h2 className="text-lg font-bold mb-4">Files</h2>
        <div className="flex mb-4">
          <input
            type="text"
            value={newFileName}
            onChange={(e) => setNewFileName(e.target.value)}
            placeholder="New file name"
            className="bg-gray-800 text-white p-2 rounded-l"
          />
          <button
            onClick={handleCreateFile}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-r"
          >
            Create
          </button>
        </div>
        <ul>
          {files.map((file) => (
            <li
              key={file._id}
              className={`flex justify-between items-center cursor-pointer p-2 rounded ${
                selectedFile?._id === file._id ? 'bg-gray-700' : ''
              }`}
              onClick={() => setSelectedFile(file)}
            >
              <span>{file.name}</span>
              <button
                onClick={() => handleDeleteFile(file._id)}
                className="text-red-500 hover:text-red-700"
              >
                Delete
              </button>
            </li>
          ))}
        </ul>
      </div>
      <div className="w-3/4">
        {selectedFile && (
          <CodeMirror
            value={selectedFile.content}
            height="100vh"
            theme={okaidia}
            extensions={[javascript({ jsx: true })]}
            onChange={handleContentChange}
          />
        )}
      </div>
    </div>
  );
};

export default IDE;
